# @Time: 2025/8/13 15:07
# @Author: lvjing
from tortoise import models, fields

from apps.ai_api_auto.enums import HttpMethod


class YapiBaseInfoModel(models.Model):
    """
    yapi基础信息表
    """
    id = fields.IntField(pk=True, auto_increment=True, description="主键id")
    yapi_base_url = fields.CharField(max_length=255, description="yapi基础地址")
    yapi_token = fields.CharField(max_length=255, description="yapi token认证")
    description = fields.CharField(max_length=255, description="描述", null=True)
    project = fields.ForeignKeyField('models.ProjectModel', related_name="yapi", on_delete=fields.SET_NULL, null=True)
    creator = fields.ForeignKeyField('models.UsersModel', related_name="create_yapi", on_delete=fields.SET_NULL,
                                     null=True)
    updater = fields.ForeignKeyField('models.UsersModel', related_name="update_yapi", on_delete=fields.SET_NULL,
                                     null=True)
    create_time = fields.DatetimeField(auto_now_add=True, description="创建时间")
    update_time = fields.DatetimeField(auto_now=True, description="更新时间")

    def __str__(self):
        return self.id

    class Meta:
        table = "yapi_base_info"
        table_description = "yapi基础信息表"
        ordering = ['-id']


class YapiApiInfoModel(models.Model):
    """
    yapi获取接口信息表
    """
    id = fields.IntField(pk=True, auto_increment=True, description="主键id")
    api_name = fields.CharField(max_length=255, description="接口名称")
    api_path = fields.CharField(max_length=255, description="接口请求地址")
    api_method = fields.CharField(max_length=255, description="接口请求方式")
    api_meta_data = fields.JSONField(description="接口元数据")
    yapi_base = fields.ForeignKeyField('models.YapiBaseInfoModel', related_name="api_infos", on_delete=fields.CASCADE,
                                       description="关联的YAPI基础信息")
    project = fields.ForeignKeyField('models.ProjectModel', related_name="yapi_api", on_delete=fields.SET_NULL,
                                     null=True)
    creator = fields.ForeignKeyField('models.UsersModel', related_name="create_yapi_api", on_delete=fields.SET_NULL,
                                     null=True)
    updater = fields.ForeignKeyField('models.UsersModel', related_name="update_yapi_api", on_delete=fields.SET_NULL,
                                     null=True)
    create_time = fields.DatetimeField(auto_now_add=True, description="创建时间")
    update_time = fields.DatetimeField(auto_now=True, description="更新时间")

    def __str__(self):
        return self.id

    class Meta:
        table = "yapi_api_info"
        table_description = "yapi获取接口信息表"
        ordering = ['-id']


class ApiDocumentUploadModel(models.Model):
    """
    接口文档上传记录表
    """
    id = fields.IntField(pk=True, auto_increment=True, description="主键id")
    document_name = fields.CharField(max_length=255, description="文档名称")
    original_filename = fields.CharField(max_length=255, description="原始文件名")
    file_type = fields.CharField(max_length=50, description="文件类型")
    file_size = fields.IntField(description="文件大小(字节)")
    oss_file_id = fields.CharField(max_length=255, description="OSS文件ID")
    oss_file_url = fields.CharField(max_length=500, description="OSS文件URL", null=True)
    document_category = fields.CharField(max_length=50, description="文档分类", default="api_doc")
    description = fields.TextField(description="文档描述", null=True)
    is_converted_to_pdf = fields.BooleanField(description="是否已转换为PDF", default=False)
    converted_from_type = fields.CharField(max_length=50, description="转换前文件类型", null=True)
    project = fields.ForeignKeyField('models.ProjectModel', related_name="api_documents", on_delete=fields.SET_NULL,
                                     null=True, description="关联项目")
    creator = fields.ForeignKeyField('models.UsersModel', related_name="create_api_documents",
                                     on_delete=fields.SET_NULL,
                                     null=True, description="创建者")
    updater = fields.ForeignKeyField('models.UsersModel', related_name="update_api_documents",
                                     on_delete=fields.SET_NULL,
                                     null=True, description="更新者")
    create_time = fields.DatetimeField(auto_now_add=True, description="创建时间")
    update_time = fields.DatetimeField(auto_now=True, description="更新时间")

    def __str__(self):
        return self.document_name

    class Meta:
        table = "api_document_upload"
        table_description = "接口文档上传记录表"
        ordering = ['-id']


class ApiDetail(models.Model):
    """接口信息表 - 存储解析后的接口详细信息"""
    id = fields.IntField(pk=True, auto_increment=True, description="主键id")
    api_id = fields.CharField(max_length=100, unique=True, description="接口ID", index=True)
    # 基本信息
    name = fields.CharField(max_length=200, description="接口名称")
    path = fields.CharField(max_length=500, description="接口路径", index=True)
    method = fields.CharEnumField(HttpMethod, description="HTTP方法", index=True)
    summary = fields.TextField(default="", description="接口摘要")
    description = fields.TextField(default="", description="接口详细描述")
    # API信息
    api_title = fields.CharField(max_length=200, null=True, description="API标题")
    api_version = fields.CharField(max_length=50, null=True, description="API版本")
    base_url = fields.CharField(max_length=500, null=True, description="基础URL")
    # 认证和安全
    auth_required = fields.BooleanField(default=False, description="是否需要认证")
    auth_type = fields.CharField(max_length=50, null=True, description="认证类型")
    # 扩展信息
    extended_info = fields.JSONField(default=dict, description="扩展信息")
    raw_data = fields.JSONField(default=dict, description="原始解析数据")
    # 状态管理
    is_active = fields.BooleanField(default=True, description="是否激活")
    create_time = fields.DatetimeField(auto_now_add=True, description="创建时间")
    update_time = fields.DatetimeField(auto_now=True, description="更新时间")

    class Meta:
        table = "api_detail"
        table_description = "接口详情信息表"
        indexes = [
            ("document_id", "method", "path"),
            ("name", "is_active"),
        ]


class ApiParameter(models.Model):
    """接口参数表"""
    id = fields.IntField(pk=True, auto_increment=True, description="主键id")
    parameter_id = fields.CharField(max_length=100, unique=True, description="参数ID", index=True)
    api = fields.ForeignKeyField("models.ApiDetail", related_name="interface", description="关联接口",
                                 index=True)
    # 参数基本信息
    name = fields.CharField(max_length=100, description="参数名称", index=True)
    location = fields.CharField(max_length=20, description="参数位置")  # header, query, path, body, form
    data_type = fields.CharField(max_length=50, description="数据类型")  # string, integer, boolean, object, array
    required = fields.BooleanField(default=False, description="是否必需")
    # 参数描述
    description = fields.TextField(default="", description="参数描述")
    # 约束条件
    constraints = fields.JSONField(default=dict, description="约束条件")  # min, max, pattern, enum等
    # 嵌套结构（用于object类型）
    create_time = fields.DatetimeField(auto_now_add=True, description="创建时间")
    update_time = fields.DatetimeField(auto_now=True, description="更新时间")

    class Meta:
        table = "api_parameters"
        table_description = "接口参数表"
        indexes = [
            ("api_id", "location"),
            ("name", "required"),
            ("data_type", "location")
        ]


class ApiResponse(models.Model):
    """接口响应表"""
    id = fields.IntField(pk=True, auto_increment=True, description="主键id")
    response_id = fields.CharField(max_length=100, unique=True, description="响应ID", index=True)
    api = fields.ForeignKeyField("models.ApiDetail", related_name="interface", description="关联接口",
                                 index=True)
    # 响应基本信息
    status_code = fields.CharField(max_length=10, description="状态码", index=True)  # 200, 400, 500等
    description = fields.TextField(default="", description="响应描述")
    content_type = fields.CharField(max_length=100, default="application/json", description="内容类型")
    # 响应结构
    response_schema = fields.JSONField(null=True, description="响应结构定义")
    # 响应头
    headers = fields.JSONField(default=dict, description="响应头定义")
    create_time = fields.DatetimeField(auto_now_add=True, description="创建时间")
    update_time = fields.DatetimeField(auto_now=True, description="更新时间")

    class Meta:
        table = "api_responses"
        table_description = "接口响应表"
        indexes = [
            ("api_id", "status_code"),
            ("status_code", "content_type")
        ]
