2025-08-27 10:08:34,434 - autogen_core - ERROR - Error processing publish message for api_doc_parser/api_orchestrator
Traceback (most recent call last):
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/autogen_core/_single_threaded_agent_runtime.py", line 605, in _on_message
    return await agent.on_message(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/autogen_core/_base_agent.py", line 119, in on_message
    return await self.on_message_impl(message, ctx)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/autogen_core/_routed_agent.py", line 485, in on_message_impl
    return await h(self, message, ctx)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/autogen_core/_routed_agent.py", line 149, in wrapper
    return_value = await func(self, message, ctx)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/automatrix/apps/ai_api_auto/agents/api_docs_paraser_agent.py", line 63, in handle_api_content_parse_request
    parse_result = await self._intelligent_parse_document_content(message.api_file_content)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/automatrix/apps/ai_api_auto/agents/api_docs_paraser_agent.py", line 98, in _intelligent_parse_document_content
    result_content = await self._run_assistant_agent(task_prompt)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/automatrix/apps/ai_api_auto/agents/base_api_agent.py", line 161, in _run_assistant_agent
    result = await self.assistant_agent.run(task=task)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/autogen_agentchat/agents/_base_chat_agent.py", line 136, in run
    response = await self.on_messages(input_messages, cancellation_token)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/autogen_agentchat/agents/_assistant_agent.py", line 806, in on_messages
    async for message in self.on_messages_stream(messages, cancellation_token):
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/autogen_agentchat/agents/_assistant_agent.py", line 852, in on_messages_stream
    async for inference_output in self._call_llm(
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/autogen_agentchat/agents/_assistant_agent.py", line 965, in _call_llm
    async for chunk in model_client.create_stream(
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/autogen_ext/models/openai/_openai_client.py", line 824, in create_stream
    async for chunk in chunks:
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/autogen_ext/models/openai/_openai_client.py", line 1021, in _create_stream_chunks
    chunk = await chunk_future
            ^^^^^^^^^^^^^^^^^^
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/openai/_streaming.py", line 144, in __anext__
    return await self._iterator.__anext__()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/openai/_streaming.py", line 160, in __stream__
    async for sse in iterator:
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/openai/_streaming.py", line 151, in _iter_events
    async for sse in self._decoder.aiter_bytes(self.response.aiter_bytes()):
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/openai/_streaming.py", line 302, in aiter_bytes
    async for chunk in self._aiter_chunks(iterator):
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/openai/_streaming.py", line 313, in _aiter_chunks
    async for chunk in iterator:
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/httpx/_models.py", line 997, in aiter_bytes
    async for raw_bytes in self.aiter_raw():
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/httpx/_models.py", line 1055, in aiter_raw
    async for raw_stream_bytes in self.stream:
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/httpx/_client.py", line 176, in __aiter__
    async for chunk in self._stream:
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/httpx/_transports/default.py", line 271, in __aiter__
    async for part in self._httpcore_stream:
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/httpcore/_async/connection_pool.py", line 407, in __aiter__
    raise exc from None
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/httpcore/_async/connection_pool.py", line 403, in __aiter__
    async for part in self._stream:
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/httpcore/_async/http11.py", line 342, in __aiter__
    raise exc
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/httpcore/_async/http11.py", line 334, in __aiter__
    async for chunk in self._connection._receive_response_body(**kwargs):
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/httpcore/_async/http11.py", line 203, in _receive_response_body
    event = await self._receive_event(timeout=timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/httpcore/_async/http11.py", line 217, in _receive_event
    data = await self._network_stream.read(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/httpcore/_backends/anyio.py", line 35, in read
    return await self._stream.receive(max_bytes=max_bytes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/anyio/streams/tls.py", line 219, in receive
    data = await self._call_sslobject_method(self._ssl_object.read, max_bytes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/anyio/streams/tls.py", line 162, in _call_sslobject_method
    data = await self.transport_stream.receive()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/anyio/_backends/_asyncio.py", line 1254, in receive
    await self._protocol.read_event.wait()
  File "/usr/local/var/pyenv/versions/3.12.8/lib/python3.12/asyncio/locks.py", line 212, in wait
    await fut
asyncio.exceptions.CancelledError
2025-08-27 10:08:34,457 - autogen_core.events - INFO - {"payload": "{\"session_id\":\"697c6f85-a57a-4c4a-b429-5f35cee216d8\",\"api_file_content\":\"\\nAPI\u6587\u6863\uff1a\u8ba2\u5355\u7ba1\u7406\u7cfb\u7edf\\n\\n\u57fa\u7840\u4fe1\u606f\uff1a\\n- \u670d\u52a1\u540d\u79f0\uff1a\u8ba2\u5355\u7ba1\u7406API\\n- \u7248\u672c\uff1av1.0\\n- \u57fa\u7840URL\uff1ahttps://order.example.com/api\\n\\n\u63a5\u53e3\u5217\u8868\uff1a\\n\\n1. \u521b\u5efa\u8ba2\u5355\\n   - \u8def\u5f84\uff1aPOST /orders\\n   - \u63cf\u8ff0\uff1a\u521b\u5efa\u65b0\u7684\u8ba2\u5355\\n   - \u53c2\u6570\uff1a\\n     * user_id (\u5fc5\u9700): \u7528\u6237ID\\n     * items (\u5fc5\u9700): \u5546\u54c1\u5217\u8868\\n     * total_amount (\u5fc5\u9700): \u603b\u91d1\u989d\\n   - \u54cd\u5e94\uff1a\\n     * 200: \u521b\u5efa\u6210\u529f\\n     * 400: \u53c2\u6570\u9519\u8bef\\n\\n2. \u67e5\u8be2\u8ba2\u5355\\n   - \u8def\u5f84\uff1aGET /orders/{order_id}\\n   - \u63cf\u8ff0\uff1a\u6839\u636e\u8ba2\u5355ID\u67e5\u8be2\u8ba2\u5355\u8be6\u60c5\\n   - \u53c2\u6570\uff1a\\n     * order_id (\u8def\u5f84\u53c2\u6570): \u8ba2\u5355ID\\n   - \u54cd\u5e94\uff1a\\n     * 200: \u67e5\u8be2\u6210\u529f\\n     * 404: \u8ba2\u5355\u4e0d\u5b58\u5728\\n            \"}", "handling_agent": "api_doc_parser/api_orchestrator", "exception": "", "type": "MessageHandlerException"}
2025-08-27 10:08:34,457 - autogen_core - ERROR - Error processing publish message for api_doc_parser/api_orchestrator
Traceback (most recent call last):
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/autogen_core/_single_threaded_agent_runtime.py", line 605, in _on_message
    return await agent.on_message(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/autogen_core/_base_agent.py", line 119, in on_message
    return await self.on_message_impl(message, ctx)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/autogen_core/_routed_agent.py", line 485, in on_message_impl
    return await h(self, message, ctx)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/autogen_core/_routed_agent.py", line 149, in wrapper
    return_value = await func(self, message, ctx)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/automatrix/apps/ai_api_auto/agents/api_docs_paraser_agent.py", line 63, in handle_api_content_parse_request
    parse_result = await self._intelligent_parse_document_content(message.api_file_content)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/automatrix/apps/ai_api_auto/agents/api_docs_paraser_agent.py", line 98, in _intelligent_parse_document_content
    result_content = await self._run_assistant_agent(task_prompt)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/automatrix/apps/ai_api_auto/agents/base_api_agent.py", line 161, in _run_assistant_agent
    result = await self.assistant_agent.run(task=task)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/autogen_agentchat/agents/_base_chat_agent.py", line 136, in run
    response = await self.on_messages(input_messages, cancellation_token)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/autogen_agentchat/agents/_assistant_agent.py", line 806, in on_messages
    async for message in self.on_messages_stream(messages, cancellation_token):
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/autogen_agentchat/agents/_assistant_agent.py", line 852, in on_messages_stream
    async for inference_output in self._call_llm(
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/autogen_agentchat/agents/_assistant_agent.py", line 965, in _call_llm
    async for chunk in model_client.create_stream(
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/autogen_ext/models/openai/_openai_client.py", line 824, in create_stream
    async for chunk in chunks:
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/autogen_ext/models/openai/_openai_client.py", line 1021, in _create_stream_chunks
    chunk = await chunk_future
            ^^^^^^^^^^^^^^^^^^
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/openai/_streaming.py", line 144, in __anext__
    return await self._iterator.__anext__()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/openai/_streaming.py", line 160, in __stream__
    async for sse in iterator:
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/openai/_streaming.py", line 151, in _iter_events
    async for sse in self._decoder.aiter_bytes(self.response.aiter_bytes()):
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/openai/_streaming.py", line 302, in aiter_bytes
    async for chunk in self._aiter_chunks(iterator):
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/openai/_streaming.py", line 313, in _aiter_chunks
    async for chunk in iterator:
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/httpx/_models.py", line 997, in aiter_bytes
    async for raw_bytes in self.aiter_raw():
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/httpx/_models.py", line 1055, in aiter_raw
    async for raw_stream_bytes in self.stream:
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/httpx/_client.py", line 176, in __aiter__
    async for chunk in self._stream:
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/httpx/_transports/default.py", line 271, in __aiter__
    async for part in self._httpcore_stream:
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/httpcore/_async/connection_pool.py", line 407, in __aiter__
    raise exc from None
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/httpcore/_async/connection_pool.py", line 403, in __aiter__
    async for part in self._stream:
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/httpcore/_async/http11.py", line 342, in __aiter__
    raise exc
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/httpcore/_async/http11.py", line 334, in __aiter__
    async for chunk in self._connection._receive_response_body(**kwargs):
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/httpcore/_async/http11.py", line 203, in _receive_response_body
    event = await self._receive_event(timeout=timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/httpcore/_async/http11.py", line 217, in _receive_event
    data = await self._network_stream.read(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/httpcore/_backends/anyio.py", line 35, in read
    return await self._stream.receive(max_bytes=max_bytes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/anyio/streams/tls.py", line 219, in receive
    data = await self._call_sslobject_method(self._ssl_object.read, max_bytes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/anyio/streams/tls.py", line 162, in _call_sslobject_method
    data = await self.transport_stream.receive()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/anyio/_backends/_asyncio.py", line 1254, in receive
    await self._protocol.read_event.wait()
  File "/usr/local/var/pyenv/versions/3.12.8/lib/python3.12/asyncio/locks.py", line 212, in wait
    await fut
asyncio.exceptions.CancelledError
2025-08-27 10:08:34,459 - autogen_core.events - INFO - {"payload": "{\"session_id\":\"b4b7c904-e592-423d-9244-e5277e5bac1c\",\"api_file_content\":\"\\n{\\n  \\\"swagger\\\": \\\"2.0\\\",\\n  \\\"info\\\": {\\n    \\\"title\\\": \\\"\u5546\u54c1\u7ba1\u7406API\\\",\\n    \\\"version\\\": \\\"2.0.0\\\",\\n    \\\"description\\\": \\\"\u7535\u5546\u7cfb\u7edf\u5546\u54c1\u7ba1\u7406\u63a5\u53e3\\\"\\n  },\\n  \\\"host\\\": \\\"shop.example.com\\\",\\n  \\\"basePath\\\": \\\"/api/v2\\\",\\n  \\\"schemes\\\": [\\\"https\\\"],\\n  \\\"paths\\\": {\\n    \\\"/products\\\": {\\n      \\\"get\\\": {\\n        \\\"summary\\\": \\\"\u83b7\u53d6\u5546\u54c1\u5217\u8868\\\",\\n        \\\"parameters\\\": [\\n          {\\n            \\\"name\\\": \\\"category\\\",\\n            \\\"in\\\": \\\"query\\\",\\n            \\\"type\\\": \\\"string\\\",\\n            \\\"description\\\": \\\"\u5546\u54c1\u5206\u7c7b\\\"\\n          }\\n        ],\\n        \\\"responses\\\": {\\n          \\\"200\\\": {\\n            \\\"description\\\": \\\"\u6210\u529f\\\"\\n          }\\n        }\\n      }\\n    }\\n  }\\n}\\n            \"}", "handling_agent": "api_doc_parser/api_orchestrator", "exception": "", "type": "MessageHandlerException"}
2025-08-27 10:08:34,459 - autogen_core - ERROR - Error processing publish message for api_doc_parser/api_orchestrator
Traceback (most recent call last):
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/autogen_core/_single_threaded_agent_runtime.py", line 605, in _on_message
    return await agent.on_message(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/autogen_core/_base_agent.py", line 119, in on_message
    return await self.on_message_impl(message, ctx)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/autogen_core/_routed_agent.py", line 485, in on_message_impl
    return await h(self, message, ctx)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/autogen_core/_routed_agent.py", line 149, in wrapper
    return_value = await func(self, message, ctx)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/automatrix/apps/ai_api_auto/agents/api_docs_paraser_agent.py", line 63, in handle_api_content_parse_request
    parse_result = await self._intelligent_parse_document_content(message.api_file_content)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/automatrix/apps/ai_api_auto/agents/api_docs_paraser_agent.py", line 98, in _intelligent_parse_document_content
    result_content = await self._run_assistant_agent(task_prompt)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/automatrix/apps/ai_api_auto/agents/base_api_agent.py", line 161, in _run_assistant_agent
    result = await self.assistant_agent.run(task=task)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/autogen_agentchat/agents/_base_chat_agent.py", line 136, in run
    response = await self.on_messages(input_messages, cancellation_token)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/autogen_agentchat/agents/_assistant_agent.py", line 806, in on_messages
    async for message in self.on_messages_stream(messages, cancellation_token):
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/autogen_agentchat/agents/_assistant_agent.py", line 852, in on_messages_stream
    async for inference_output in self._call_llm(
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/autogen_agentchat/agents/_assistant_agent.py", line 965, in _call_llm
    async for chunk in model_client.create_stream(
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/autogen_ext/models/openai/_openai_client.py", line 824, in create_stream
    async for chunk in chunks:
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/autogen_ext/models/openai/_openai_client.py", line 1021, in _create_stream_chunks
    chunk = await chunk_future
            ^^^^^^^^^^^^^^^^^^
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/openai/_streaming.py", line 144, in __anext__
    return await self._iterator.__anext__()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/openai/_streaming.py", line 160, in __stream__
    async for sse in iterator:
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/openai/_streaming.py", line 151, in _iter_events
    async for sse in self._decoder.aiter_bytes(self.response.aiter_bytes()):
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/openai/_streaming.py", line 302, in aiter_bytes
    async for chunk in self._aiter_chunks(iterator):
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/openai/_streaming.py", line 313, in _aiter_chunks
    async for chunk in iterator:
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/httpx/_models.py", line 997, in aiter_bytes
    async for raw_bytes in self.aiter_raw():
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/httpx/_models.py", line 1055, in aiter_raw
    async for raw_stream_bytes in self.stream:
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/httpx/_client.py", line 176, in __aiter__
    async for chunk in self._stream:
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/httpx/_transports/default.py", line 271, in __aiter__
    async for part in self._httpcore_stream:
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/httpcore/_async/connection_pool.py", line 407, in __aiter__
    raise exc from None
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/httpcore/_async/connection_pool.py", line 403, in __aiter__
    async for part in self._stream:
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/httpcore/_async/http11.py", line 342, in __aiter__
    raise exc
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/httpcore/_async/http11.py", line 334, in __aiter__
    async for chunk in self._connection._receive_response_body(**kwargs):
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/httpcore/_async/http11.py", line 203, in _receive_response_body
    event = await self._receive_event(timeout=timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/httpcore/_async/http11.py", line 217, in _receive_event
    data = await self._network_stream.read(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/httpcore/_backends/anyio.py", line 35, in read
    return await self._stream.receive(max_bytes=max_bytes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/anyio/streams/tls.py", line 219, in receive
    data = await self._call_sslobject_method(self._ssl_object.read, max_bytes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/anyio/streams/tls.py", line 162, in _call_sslobject_method
    data = await self.transport_stream.receive()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/anyio/_backends/_asyncio.py", line 1254, in receive
    await self._protocol.read_event.wait()
  File "/usr/local/var/pyenv/versions/3.12.8/lib/python3.12/asyncio/locks.py", line 212, in wait
    await fut
asyncio.exceptions.CancelledError
2025-08-27 10:08:34,460 - autogen_core.events - INFO - {"payload": "{\"session_id\":\"e69cc5a8-394d-4025-9abf-459153689f7d\",\"api_file_content\":\"\\n{\\n  \\\"openapi\\\": \\\"3.0.0\\\",\\n  \\\"info\\\": {\\n    \\\"title\\\": \\\"\u7528\u6237\u7ba1\u7406API\\\",\\n    \\\"version\\\": \\\"1.0.0\\\",\\n    \\\"description\\\": \\\"\u7b80\u5355\u7684\u7528\u6237\u7ba1\u7406\u7cfb\u7edfAPI\\\"\\n  },\\n  \\\"servers\\\": [\\n    {\\n      \\\"url\\\": \\\"https://api.example.com/v1\\\",\\n      \\\"description\\\": \\\"\u751f\u4ea7\u73af\u5883\\\"\\n    }\\n  ],\\n  \\\"paths\\\": {\\n    \\\"/users\\\": {\\n      \\\"get\\\": {\\n        \\\"summary\\\": \\\"\u83b7\u53d6\u7528\u6237\u5217\u8868\\\",\\n        \\\"description\\\": \\\"\u5206\u9875\u83b7\u53d6\u6240\u6709\u7528\u6237\u4fe1\u606f\\\",\\n        \\\"parameters\\\": [\\n          {\\n            \\\"name\\\": \\\"page\\\",\\n            \\\"in\\\": \\\"query\\\",\\n            \\\"description\\\": \\\"\u9875\u7801\\\",\\n            \\\"required\\\": false,\\n            \\\"schema\\\": {\\n              \\\"type\\\": \\\"integer\\\",\\n              \\\"default\\\": 1\\n            }\\n          },\\n          {\\n            \\\"name\\\": \\\"limit\\\",\\n            \\\"in\\\": \\\"query\\\", \\n            \\\"description\\\": \\\"\u6bcf\u9875\u6570\u91cf\\\",\\n            \\\"required\\\": false,\\n            \\\"schema\\\": {\\n              \\\"type\\\": \\\"integer\\\",\\n              \\\"default\\\": 10\\n            }\\n          }\\n        ],\\n        \\\"responses\\\": {\\n          \\\"200\\\": {\\n            \\\"description\\\": \\\"\u6210\u529f\u8fd4\u56de\u7528\u6237\u5217\u8868\\\",\\n            \\\"content\\\": {\\n              \\\"application/json\\\": {\\n                \\\"schema\\\": {\\n                  \\\"type\\\": \\\"object\\\",\\n                  \\\"properties\\\": {\\n                    \\\"code\\\": {\\\"type\\\": \\\"integer\\\"},\\n                    \\\"message\\\": {\\\"type\\\": \\\"string\\\"},\\n                    \\\"data\\\": {\\n                      \\\"type\\\": \\\"array\\\",\\n                      \\\"items\\\": {\\n                        \\\"type\\\": \\\"object\\\",\\n                        \\\"properties\\\": {\\n                          \\\"id\\\": {\\\"type\\\": \\\"integer\\\"},\\n                          \\\"name\\\": {\\\"type\\\": \\\"string\\\"},\\n                          \\\"email\\\": {\\\"type\\\": \\\"string\\\"}\\n                        }\\n                      }\\n                    }\\n                  }\\n                }\\n              }\\n            }\\n          }\\n        }\\n      },\\n      \\\"post\\\": {\\n        \\\"summary\\\": \\\"\u521b\u5efa\u65b0\u7528\u6237\\\",\\n        \\\"description\\\": \\\"\u521b\u5efa\u4e00\u4e2a\u65b0\u7684\u7528\u6237\u8d26\u6237\\\",\\n        \\\"requestBody\\\": {\\n          \\\"required\\\": true,\\n          \\\"content\\\": {\\n            \\\"application/json\\\": {\\n              \\\"schema\\\": {\\n                \\\"type\\\": \\\"object\\\",\\n                \\\"required\\\": [\\\"name\\\", \\\"email\\\"],\\n                \\\"properties\\\": {\\n                  \\\"name\\\": {\\\"type\\\": \\\"string\\\"},\\n                  \\\"email\\\": {\\\"type\\\": \\\"string\\\"},\\n                  \\\"age\\\": {\\\"type\\\": \\\"integer\\\"}\\n                }\\n              }\\n            }\\n          }\\n        },\\n        \\\"responses\\\": {\\n          \\\"201\\\": {\\n            \\\"description\\\": \\\"\u7528\u6237\u521b\u5efa\u6210\u529f\\\"\\n          },\\n          \\\"400\\\": {\\n            \\\"description\\\": \\\"\u8bf7\u6c42\u53c2\u6570\u9519\u8bef\\\"\\n          }\\n        }\\n      }\\n    },\\n    \\\"/users/{id}\\\": {\\n      \\\"get\\\": {\\n        \\\"summary\\\": \\\"\u83b7\u53d6\u7528\u6237\u8be6\u60c5\\\",\\n        \\\"parameters\\\": [\\n          {\\n            \\\"name\\\": \\\"id\\\",\\n            \\\"in\\\": \\\"path\\\",\\n            \\\"required\\\": true,\\n            \\\"schema\\\": {\\n              \\\"type\\\": \\\"integer\\\"\\n            }\\n          }\\n        ],\\n        \\\"responses\\\": {\\n          \\\"200\\\": {\\n            \\\"description\\\": \\\"\u6210\u529f\u8fd4\u56de\u7528\u6237\u8be6\u60c5\\\"\\n          },\\n          \\\"404\\\": {\\n            \\\"description\\\": \\\"\u7528\u6237\u4e0d\u5b58\u5728\\\"\\n          }\\n        }\\n      }\\n    }\\n  }\\n}\\n            \"}", "handling_agent": "api_doc_parser/api_orchestrator", "exception": "", "type": "MessageHandlerException"}
2025-08-27 10:08:34,461 - asyncio - ERROR - unhandled exception during asyncio.run() shutdown
task: <Task finished name='Task-12' coro=<SingleThreadedAgentRuntime._process_publish() done, defined at /usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/autogen_core/_single_threaded_agent_runtime.py:556> exception=ValueError('task_done() called too many times')>
Traceback (most recent call last):
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/autogen_core/_single_threaded_agent_runtime.py", line 628, in _process_publish
    self._message_queue.task_done()
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/autogen_core/_queue.py", line 222, in task_done
    raise ValueError("task_done() called too many times")
ValueError: task_done() called too many times
2025-08-27 10:08:34,462 - asyncio - ERROR - unhandled exception during asyncio.run() shutdown
task: <Task finished name='Task-3' coro=<SingleThreadedAgentRuntime._process_publish() done, defined at /usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/autogen_core/_single_threaded_agent_runtime.py:556> exception=ValueError('task_done() called too many times')>
Traceback (most recent call last):
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/autogen_core/_single_threaded_agent_runtime.py", line 628, in _process_publish
    self._message_queue.task_done()
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/autogen_core/_queue.py", line 222, in task_done
    raise ValueError("task_done() called too many times")
ValueError: task_done() called too many times
2025-08-27 10:08:34,463 - asyncio - ERROR - unhandled exception during asyncio.run() shutdown
task: <Task finished name='Task-54' coro=<SingleThreadedAgentRuntime._process_publish() done, defined at /usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/autogen_core/_single_threaded_agent_runtime.py:556> exception=ValueError('task_done() called too many times')>
Traceback (most recent call last):
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/autogen_core/_single_threaded_agent_runtime.py", line 628, in _process_publish
    self._message_queue.task_done()
  File "/usr/local/var/pyenv/versions/auto-matrix-3.12/lib/python3.12/site-packages/autogen_core/_queue.py", line 222, in task_done
    raise ValueError("task_done() called too many times")
ValueError: task_done() called too many times
