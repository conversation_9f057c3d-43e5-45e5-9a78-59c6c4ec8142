# @Time: 2025/8/27 
# @Author: lvjing
"""
API解析智能体流程测试脚本

测试从ApiOrchestrator.process_api_document_content开始到ApiDocParserAgent完成解析的整个流程
不使用单元测试框架，直接运行并输出结果
"""
import asyncio
import json
import logging
import uuid
from datetime import datetime
from typing import Dict, Any


def safe_json_dumps(obj, **kwargs):
    """安全的JSON序列化，处理datetime等不可序列化对象"""

    def json_serializer(obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        raise TypeError(f"Object of type {obj.__class__.__name__} is not JSON serializable")

    return json.dumps(obj, default=json_serializer, **kwargs)


# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class ApiParserFlowRunner:
    """API解析流程测试运行器"""

    def __init__(self):
        self.orchestrator = None
        self.test_results = []

    async def setup(self):
        """初始化测试环境"""
        print("=" * 80)
        print("🚀 初始化API解析智能体流程测试环境")
        print("=" * 80)

        try:
            # 导入必要的模块
            from apps.ai_api_auto.services.api_orchestrator_service import ApiAutomationOrchestrator

            # 创建编排器实例
            self.orchestrator = ApiAutomationOrchestrator()

            # 初始化编排器
            await self.orchestrator.initialize()

            print("✅ 测试环境初始化完成")
            return True

        except Exception as e:
            print(f"❌ 测试环境初始化失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

    def create_test_api_content(self, test_case_name: str) -> str:
        """创建测试用的API文档内容"""
        test_contents = {
            "openapi_simple": '''
{
  "openapi": "3.0.0",
  "info": {
    "title": "用户管理API",
    "version": "1.0.0",
    "description": "简单的用户管理系统API"
  },
  "servers": [
    {
      "url": "https://api.example.com/v1",
      "description": "生产环境"
    }
  ],
  "paths": {
    "/users": {
      "get": {
        "summary": "获取用户列表",
        "description": "分页获取所有用户信息",
        "parameters": [
          {
            "name": "page",
            "in": "query",
            "description": "页码",
            "required": false,
            "schema": {
              "type": "integer",
              "default": 1
            }
          },
          {
            "name": "limit",
            "in": "query", 
            "description": "每页数量",
            "required": false,
            "schema": {
              "type": "integer",
              "default": 10
            }
          }
        ],
        "responses": {
          "200": {
            "description": "成功返回用户列表",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "code": {"type": "integer"},
                    "message": {"type": "string"},
                    "data": {
                      "type": "array",
                      "items": {
                        "type": "object",
                        "properties": {
                          "id": {"type": "integer"},
                          "name": {"type": "string"},
                          "email": {"type": "string"}
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      },
      "post": {
        "summary": "创建新用户",
        "description": "创建一个新的用户账户",
        "requestBody": {
          "required": true,
          "content": {
            "application/json": {
              "schema": {
                "type": "object",
                "required": ["name", "email"],
                "properties": {
                  "name": {"type": "string"},
                  "email": {"type": "string"},
                  "age": {"type": "integer"}
                }
              }
            }
          }
        },
        "responses": {
          "201": {
            "description": "用户创建成功"
          },
          "400": {
            "description": "请求参数错误"
          }
        }
      }
    },
    "/users/{id}": {
      "get": {
        "summary": "获取用户详情",
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "成功返回用户详情"
          },
          "404": {
            "description": "用户不存在"
          }
        }
      }
    }
  }
}
            ''',

            "swagger_simple": '''
{
  "swagger": "2.0",
  "info": {
    "title": "商品管理API",
    "version": "2.0.0",
    "description": "电商系统商品管理接口"
  },
  "host": "shop.example.com",
  "basePath": "/api/v2",
  "schemes": ["https"],
  "paths": {
    "/products": {
      "get": {
        "summary": "获取商品列表",
        "parameters": [
          {
            "name": "category",
            "in": "query",
            "type": "string",
            "description": "商品分类"
          }
        ],
        "responses": {
          "200": {
            "description": "成功"
          }
        }
      }
    }
  }
}
            ''',

            "custom_format": '''
API文档：订单管理系统

基础信息：
- 服务名称：订单管理API
- 版本：v1.0
- 基础URL：https://order.example.com/api

接口列表：

1. 创建订单
   - 路径：POST /orders
   - 描述：创建新的订单
   - 参数：
     * user_id (必需): 用户ID
     * items (必需): 商品列表
     * total_amount (必需): 总金额
   - 响应：
     * 200: 创建成功
     * 400: 参数错误

2. 查询订单
   - 路径：GET /orders/{order_id}
   - 描述：根据订单ID查询订单详情
   - 参数：
     * order_id (路径参数): 订单ID
   - 响应：
     * 200: 查询成功
     * 404: 订单不存在
            '''
        }

        return test_contents.get(test_case_name, test_contents["openapi_simple"])

    async def run_single_test(self, test_name: str, api_content: str) -> Dict[str, Any]:
        """运行单个测试用例"""
        print(f"\n📋 开始测试用例: {test_name}")
        print("-" * 60)

        session_id = str(uuid.uuid4())
        start_time = datetime.now()

        test_result = {
            "test_name": test_name,
            "session_id": session_id,
            "start_time": start_time,
            "success": False,
            "error": None,
            "processing_time": 0,
            "orchestrator_result": None,
            "agent_completion": None
        }

        try:
            print(f"🔄 会话ID: {session_id}")
            print(f"📄 API文档内容长度: {len(api_content)} 字符")
            print(f"📄 API文档内容预览: {api_content[:200]}...")

            print("\n🎯 步骤1: 调用编排器处理API文档内容")
            orchestrator_result = await self.orchestrator.process_api_document_content(
                session_id=session_id,
                api_file_content=api_content
            )

            test_result["orchestrator_result"] = orchestrator_result
            print(f"✅ 编排器处理结果: {safe_json_dumps(orchestrator_result, indent=2, ensure_ascii=False)}")

        except Exception as e:
            print(f"❌ 测试执行失败: {str(e)}")
            test_result["error"] = str(e)
            import traceback
            traceback.print_exc()

        finally:
            test_result["processing_time"] = (datetime.now() - start_time).total_seconds()
            print(f"\n⏱️  测试用例总耗时: {test_result['processing_time']:.2f}秒")

        return test_result

    async def run_all_tests(self):
        """运行所有测试用例"""
        print("\n" + "=" * 80)
        print("🧪 开始运行所有API解析流程测试")
        print("=" * 80)

        test_cases = [
            ("OpenAPI 3.0 格式", "openapi_simple"),
            ("Swagger 2.0 格式", "swagger_simple"),
            ("自定义文档格式", "custom_format")
        ]

        all_results = []

        for test_name, content_key in test_cases:
            api_content = self.create_test_api_content(content_key)
            result = await self.run_single_test(test_name, api_content)
            all_results.append(result)
            self.test_results.append(result)

            # 测试间隔
            await asyncio.sleep(2)

        # 输出测试总结
        self.print_test_summary(all_results)

        return all_results

    def print_test_summary(self, results):
        """打印测试总结"""
        print("\n" + "=" * 80)
        print("📊 测试结果总结")
        print("=" * 80)

        total_tests = len(results)
        successful_tests = sum(1 for r in results if r["success"])
        failed_tests = total_tests - successful_tests

        print(f"总测试数: {total_tests}")
        print(f"成功: {successful_tests}")
        print(f"失败: {failed_tests}")
        print(f"成功率: {(successful_tests / total_tests * 100):.1f}%")

        print(f"\n📋 详细结果:")
        for i, result in enumerate(results, 1):
            status = "✅ 成功" if result["success"] else "❌ 失败"
            print(f"{i}. {result['test_name']}: {status} ({result['processing_time']:.2f}s)")
            if not result["success"] and result["error"]:
                print(f"   错误: {result['error']}")

        # 性能统计
        if results:
            avg_time = sum(r["processing_time"] for r in results) / len(results)
            max_time = max(r["processing_time"] for r in results)
            min_time = min(r["processing_time"] for r in results)

            print(f"\n⏱️  性能统计:")
            print(f"平均处理时间: {avg_time:.2f}秒")
            print(f"最长处理时间: {max_time:.2f}秒")
            print(f"最短处理时间: {min_time:.2f}秒")

    async def cleanup(self):
        """清理测试环境"""
        print("\n🧹 清理测试环境...")
        try:
            if self.orchestrator:
                await self.orchestrator.cleanup()
            print("✅ 测试环境清理完成")
        except Exception as e:
            print(f"❌ 清理失败: {str(e)}")


async def main():
    """主函数"""
    print("🎯 API解析智能体流程测试开始")
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    runner = ApiParserFlowRunner()

    try:
        # 初始化测试环境
        if not await runner.setup():
            print("❌ 测试环境初始化失败，退出测试")
            return

        # 运行所有测试
        await runner.run_all_tests()

    except KeyboardInterrupt:
        print("\n⚠️  测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试执行异常: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        # 清理环境
        await runner.cleanup()
        print(f"\n🏁 测试结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")


if __name__ == "__main__":
    # 运行测试
    asyncio.run(main())
